<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-08-01T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="集装箱充气袋充气加固操作流程" id="flowchart">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 开始 -->
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="20" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 操作前准备 -->
        <mxCell id="prep_title" value="操作前准备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="520" y="120" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- 环境检查 -->
        <mxCell id="env_check" value="环境检查&#xa;• 确认通风良好&#xa;• 检查消防设施" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="200" y="200" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 设备检查 -->
        <mxCell id="equip_check" value="设备检查&#xa;• 空压机检查&#xa;• 充气工具检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="520" y="200" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 人员防护 -->
        <mxCell id="safety_check" value="人员防护&#xa;• 防砸鞋&#xa;• 防切割手套" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="840" y="200" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 设备启动 -->
        <mxCell id="start_machine" value="设备启动&#xa;空载运行3分钟" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 观察检查 -->
        <mxCell id="observe_check" value="观察检查&#xa;压力表正常？&#xa;无异常噪音？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="500" y="420" width="160" height="100" as="geometry" />
        </mxCell>
        
        <!-- 异常处理1 -->
        <mxCell id="abnormal1" value="停机报修" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="760" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 充气袋安装 -->
        <mxCell id="bag_install" value="充气袋安装&#xa;• 插入货物间隙&#xa;• 预留5cm空间&#xa;• 充气口朝向操作侧" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="560" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 初始充气 -->
        <mxCell id="initial_inflate" value="初始充气&#xa;压力 ≤ 0.05MPa" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 初始检查 -->
        <mxCell id="initial_check" value="检查充气袋&#xa;均匀膨胀？&#xa;无扭曲折叠？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="500" y="780" width="160" height="100" as="geometry" />
        </mxCell>
        
        <!-- 中期充气 -->
        <mxCell id="mid_inflate" value="中期充气&#xa;压力 0.05-0.15MPa&#xa;每30秒暂停检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="920" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- 中期检查 -->
        <mxCell id="mid_check" value="检查密封性&#xa;无泄漏？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="520" y="1040" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- 最终充气 -->
        <mxCell id="final_inflate" value="最终充气&#xa;压力 0.15-0.25MPa&#xa;达到标定压力立即停止" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="1160" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 最终检查 -->
        <mxCell id="final_check" value="最终检查&#xa;压力正常？&#xa;无异常？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="530" y="1280" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- 停机流程 -->
        <mxCell id="shutdown" value="停机流程&#xa;1. 关闭充气枪阀门&#xa;2. 排空管路压缩空气&#xa;3. 关闭空压机电源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="1400" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- 设备维护 -->
        <mxCell id="maintenance" value="设备维护&#xa;• 空压机排污&#xa;• 充气管清洁存放" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="520" y="1540" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- 结束 -->
        <mxCell id="end" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="540" y="1660" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 异常处理分支 -->
        <mxCell id="abnormal2" value="充气袋异常处理&#xa;立即泄压停止充气&#xa;更换新充气袋" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="800" y="800" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="abnormal3" value="管路爆裂处理&#xa;1. 切断电源&#xa;2. 撤离人员&#xa;3. 张贴警示&#xa;4. 报告维修" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="800" y="1040" width="160" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="abnormal4" value="压力失控处理&#xa;1. 触发紧急泄压阀&#xa;2. 切断电源&#xa;3. 撤离现场&#xa;4. 报修设备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="800" y="1280" width="160" height="100" as="geometry" />
        </mxCell>
        
        <!-- 安全提示 -->
        <mxCell id="safety_note" value="安全提示：&#xa;操作者始终位于&#xa;充气袋侧面操作&#xa;禁止正对充气袋体" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="40" y="680" width="160" height="100" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" edge="1" parent="1" source="start" target="prep_title">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="prep_title" target="env_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="prep_title" target="equip_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="prep_title" target="safety_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="env_check" target="start_machine">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="270" y="300" as="sourcePoint" />
            <mxPoint x="520" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="equip_check" target="start_machine">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="safety_check" target="start_machine">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="910" y="300" as="sourcePoint" />
            <mxPoint x="620" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="start_machine" target="observe_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="observe_check" target="abnormal1">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge9_label" value="异常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge9">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="observe_check" target="bag_install">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge10_label" value="正常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge10">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge11" edge="1" parent="1" source="bag_install" target="initial_inflate">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge12" edge="1" parent="1" source="initial_inflate" target="initial_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" edge="1" parent="1" source="initial_check" target="abnormal2">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge13_label" value="异常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge13">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge14" edge="1" parent="1" source="initial_check" target="mid_inflate">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge14_label" value="正常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge14">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge15" edge="1" parent="1" source="mid_inflate" target="mid_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge16" edge="1" parent="1" source="mid_check" target="abnormal3">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge16_label" value="异常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge16">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge17" edge="1" parent="1" source="mid_check" target="final_inflate">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge17_label" value="正常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge17">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge18" edge="1" parent="1" source="final_inflate" target="final_check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge19" edge="1" parent="1" source="final_check" target="abnormal4">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge19_label" value="异常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge19">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge20" edge="1" parent="1" source="final_check" target="shutdown">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge20_label" value="正常" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge20">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge21" edge="1" parent="1" source="shutdown" target="maintenance">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge22" edge="1" parent="1" source="maintenance" target="end">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
